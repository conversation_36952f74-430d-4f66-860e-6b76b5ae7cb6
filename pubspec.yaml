name: korrency
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.

version: 1.2.19+26

environment:
  sdk: ">=3.1.1 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  flutter_screenutil: ^5.9.0
  dio: ^5.1.1
  flutter_dotenv: ^5.1.0
  shared_preferences: ^2.2.2
  date_format: ^2.0.7
  firebase_core: ^3.12.1
  firebase_messaging: ^15.2.4
  flutter_local_notifications: ^19.0.0
  currency_text_input_formatter: ^2.1.9
  intl: ^0.20.2
  path_provider: ^2.0.14
  package_info_plus: ^8.3.0
  device_info_plus: ^11.3.0
  permission_handler: ^11.4.0
  flutter_svg: ^2.0.5
  card_swiper: ^3.0.1
  pinput: ^5.0.1
  lottie: ^3.0.0
  flutter_rounded_date_picker: ^3.0.2
  iconsax: ^0.0.8
  local_auth: ^2.1.6
  cached_network_image: ^3.2.3
  get_ip_address: ^0.0.5
  dotted_border: ^2.1.0
  provider: ^6.1.2
  share_plus: ^10.1.4
  another_flushbar: ^1.12.30
  percent_indicator: ^4.2.3
  numberpicker: ^2.1.2
  shimmer: ^3.0.0
  collection: ^1.18.0
  # google_maps_webservice: ^0.0.20-nullsafety.5
  skeletonizer: ^1.1.1
  rename_app: ^1.3.2
  onfido_sdk: ^8.5.0
  appsflyer_sdk: ^6.14.3
  flutter_contacts: ^1.1.7+1
  screenshot: ^3.0.0
  freshchat_sdk: ^0.10.25
  pdf: ^3.10.8
  url_launcher: ^6.2.6
  webview_flutter: ^4.8.0
  store_redirect: ^2.0.2
  local_session_timeout: ^3.2.0
  pretty_dio_logger: ^1.4.0
  geolocator: ^13.0.3
  logging: ^1.2.0
  path: ^1.8.3
  flutter_file_downloader: ^2.1.0
  open_file: ^3.5.10
  chewie: ^1.10.0
  video_player: ^2.9.5
  # telephony: ^0.2.0
  mixpanel_flutter: ^2.4.0
  swipe_deck: ^1.1.0
  google_fonts: ^6.2.1
  fl_country_code_picker: ^0.0.4
  file_saver: ^0.2.14
  in_app_review: ^2.0.10
  firebase_analytics: ^11.3.3
  facebook_app_events: ^0.19.2

  image_gallery_saver:
    git:
      url: https://github.com/Korrency/image_gallery_saver.git

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/launcher/icon.png"
  min_sdk_android: 21
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/launcher/icon_forground.png"
  remove_alpha_ios: true

dependency_overrides:
  geolocator_android: 4.6.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/dashboard/
    - assets/images/onboard/
    - assets/images/emojis/
    - assets/svgs/
    - assets/svgs/logos/
    - assets/svgs/bottomnav/
    - assets/svgs/snackbar/
    - assets/svgs/socials/
    - assets/gif/
    - .env
    - .env.dev
    - .env.staging
    - .env.prod

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter/Inter-Regular.ttf
        - asset: assets/fonts/Inter/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter/Inter-Bold.ttf
          weight: 700
        - asset: assets/fonts/Inter/Inter-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Inter/Inter-Black.ttf
          weight: 900
        - asset: assets/fonts/Inter/Inter-Thin.ttf
          weight: 100
        - asset: assets/fonts/Inter/Inter-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/Inter/Inter-Light.ttf
          weight: 300

        - asset: assets/fonts/Inter/Inter-Bold.ttf
          weight: 700

  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
