PODS:
  - appsflyer_sdk (6.15.3):
    - Apps<PERSON>lyerFramework (= 6.15.3)
    - Flutter
  - AppsFlyerFramework (6.15.3):
    - AppsFlyerFramework/Main (= 6.15.3)
  - AppsFlyerFramework/Main (6.15.3)
  - facebook_app_events (0.0.1):
    - FBAudienceNetwork (= 6.16)
    - FBSDKCoreKit (~> 18.0)
    - Flutter
  - FBAEMKit (18.0.0):
    - FBSDKCoreKit_Basics (= 18.0.0)
  - FBAudienceNetwork (6.16.0)
  - FBSDKCoreKit (18.0.0):
    - FBAEMKit (= 18.0.0)
    - FBSDKCoreKit_Basics (= 18.0.0)
  - FBSDKCoreKit_Basics (18.0.0)
  - file_saver (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_contacts (0.0.1):
    - Flutter
  - freshchat_sdk (0.10.25):
    - Flutter
    - FreshchatSDK (= 6.3.7)
  - FreshchatSDK (6.3.7)
  - image_gallery_saver (2.0.2):
    - Flutter
  - Mixpanel-swift (4.4.0):
    - Mixpanel-swift/Complete (= 4.4.0)
  - Mixpanel-swift/Complete (4.4.0)
  - mixpanel_flutter (2.4.0):
    - Flutter
    - Mixpanel-swift (= 4.4.0)
  - Onfido (32.4.2)
  - onfido_sdk (0.0.1):
    - Flutter
    - Onfido (~> 32.4.0)
  - open_file_ios (0.0.1):
    - Flutter
  - permission_handler_apple (9.3.0):
    - Flutter
  - store_redirect (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter

DEPENDENCIES:
  - appsflyer_sdk (from `.symlinks/plugins/appsflyer_sdk/ios`)
  - facebook_app_events (from `.symlinks/plugins/facebook_app_events/ios`)
  - file_saver (from `.symlinks/plugins/file_saver/ios`)
  - Flutter (from `Flutter`)
  - flutter_contacts (from `.symlinks/plugins/flutter_contacts/ios`)
  - freshchat_sdk (from `.symlinks/plugins/freshchat_sdk/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - mixpanel_flutter (from `.symlinks/plugins/mixpanel_flutter/ios`)
  - onfido_sdk (from `.symlinks/plugins/onfido_sdk/ios`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - store_redirect (from `.symlinks/plugins/store_redirect/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - AppsFlyerFramework
    - FBAEMKit
    - FBAudienceNetwork
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FreshchatSDK
    - Mixpanel-swift
    - Onfido

EXTERNAL SOURCES:
  appsflyer_sdk:
    :path: ".symlinks/plugins/appsflyer_sdk/ios"
  facebook_app_events:
    :path: ".symlinks/plugins/facebook_app_events/ios"
  file_saver:
    :path: ".symlinks/plugins/file_saver/ios"
  Flutter:
    :path: Flutter
  flutter_contacts:
    :path: ".symlinks/plugins/flutter_contacts/ios"
  freshchat_sdk:
    :path: ".symlinks/plugins/freshchat_sdk/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  mixpanel_flutter:
    :path: ".symlinks/plugins/mixpanel_flutter/ios"
  onfido_sdk:
    :path: ".symlinks/plugins/onfido_sdk/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  store_redirect:
    :path: ".symlinks/plugins/store_redirect/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

SPEC CHECKSUMS:
  appsflyer_sdk: 0132c7cd33db42ce3daf878f02809a9f88756f9f
  AppsFlyerFramework: ad7ff0d22aa36c7f8cc4f71a5424e19b89ccb8ae
  facebook_app_events: b8348b321ea43a541f2855e1e8e9994a4b7fd3cf
  FBAEMKit: e34530df538b8eb8aeb53c35867715ba6c63ef0c
  FBAudienceNetwork: d1670939884e3a2e0ad98dca98d7e0c841417228
  FBSDKCoreKit: d3f479a69127acebb1c6aad91c1a33907bcf6c2f
  FBSDKCoreKit_Basics: 017b6dc2a1862024815a8229e75661e627ac1e29
  file_saver: 503e386464dbe118f630e17b4c2e1190fa0cf808
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_contacts: edb1c5ce76aa433e20e6cb14c615f4c0b66e0983
  freshchat_sdk: 4078d62c19b5f0163c2398b6e27600d6654ba7b9
  FreshchatSDK: 52a5fbda55f5892cc96bc0f2cd80ffe188fa106c
  image_gallery_saver: cb43cc43141711190510e92c460eb1655cd343cb
  Mixpanel-swift: 478ff46d19de4a251244a9c9a582070d4bb94cf9
  mixpanel_flutter: c2e55a95a2ae23cf27c065115ecee6226e0e0718
  Onfido: 008b801587ee7d129d88627b0546a2cf0e99288d
  onfido_sdk: 43533125915d70b2ff2f79512b22ff5f8ab4067d
  open_file_ios: 461db5853723763573e140de3193656f91990d9e
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  store_redirect: 2977747cf81689a39bd62c248c2deacb7a0d131e
  wakelock_plus: 373cfe59b235a6dd5837d0fb88791d2f13a90d56

PODFILE CHECKSUM: a57f30d18f102dd3ce366b1d62a55ecbef2158e5

COCOAPODS: 1.16.2
