import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class BankSheet extends StatefulWidget {
  const BankSheet({
    super.key,
    this.selectBankType = SelectBankType.beneficiary,
    this.bankName,
    this.recipientCurrencyId,
  });

  final SelectBankType selectBankType;
  final String? bankName;
  final int? recipientCurrencyId;

  @override
  State<BankSheet> createState() => _BankSheetState();
}

class _BankSheetState extends State<BankSheet> {
  String? _selectedBankName;

  @override
  Widget build(BuildContext context) {
    return Consumer<BankVM>(builder: (context, vm, _) {
      return ContainerWithTopBorderRadius(
        height: Sizer.screenHeight * 0.66,
        child: Column(
          children: [
            const YBox(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                    size: Sizer.radius(23),
                  ),
                )
              ],
            ),
            const YBox(10),
            CustomTextField(
              borderRadius: Sizer.height(4),
              prefixIcon: Icon(
                Iconsax.search_normal_1,
                color: AppColors.black600,
                size: Sizer.radius(20),
              ),
              hintText: 'Search bank name',
              onChanged: (val) {
                printty('val: $val');
                vm.searchBanks(val.trim());
              },
            ),
            Expanded(
              child: Builder(builder: (context) {
                if (vm.isBusy) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (vm.bankList.isEmpty) {
                  return SizedBox(
                    height: Sizer.height(400),
                    child: Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Iconsax.bank,
                            size: Sizer.radius(40),
                          ),
                          const YBox(10),
                          Text(
                            "No Bank Available",
                            style: AppTypography.text16.copyWith(
                              color: AppColors.textBlack600,
                            ),
                          ),
                          const YBox(20),
                          if (widget.recipientCurrencyId != null)
                            CustomBtn.solid(
                              height: 50,
                              width: 300,
                              onTap: () {
                                context.read<BankVM>().getBanksByCurrencyId(
                                    widget.recipientCurrencyId ?? 0);
                              },
                              text: "Reload Banks",
                            ),
                        ],
                      ),
                    ),
                  );
                }
                return RefreshIndicator(
                  onRefresh: () async {
                    if (widget.recipientCurrencyId != null) {
                      context.read<BankVM>().getBanksByCurrencyId(
                          widget.recipientCurrencyId ?? 0);
                    }
                  },
                  child: ListView.separated(
                    // shrinkWrap: true,
                    // physics: const NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.only(
                      top: Sizer.height(20),
                      bottom: Sizer.height(100),
                    ),
                    itemBuilder: (ctx, i) {
                      var bank = vm.bankList[i];
                      return InkWell(
                        onTap: () {
                          _selectedBankName = bank.name;
                          if (widget.selectBankType ==
                              SelectBankType.beneficiary) {
                            context.read<BeneficiaryVM>().setBank(bank);
                          } else {
                            context.read<SendMoneyVM>().setBank(bank);
                          }
                          vm.setBankListToStore();
                          Navigator.pop(context);
                        },
                        child: ContainerWithBluewishBg(
                          padding: EdgeInsets.symmetric(
                            vertical: Sizer.height(10),
                            horizontal: Sizer.width(16),
                          ),
                          child: WalletListTile(
                            title: bank.name ?? "",
                            trailingIconSize: 16,
                            isSelected: _selectedBankName == bank.name ||
                                widget.bankName == bank.name,
                          ),
                        ),
                      );
                    },
                    separatorBuilder: (ctx, _) => const YBox(16),
                    itemCount: vm.bankList.length,
                  ),
                );
              }),
            ),
          ],
        ),
      );
    });
  }
}
