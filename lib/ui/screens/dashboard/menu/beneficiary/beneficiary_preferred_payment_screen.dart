import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/menu/beneficiary/bank_transfer_screen.dart';

class BeneficiaryPreferredMethodScreen extends StatelessWidget {
  const BeneficiaryPreferredMethodScreen({
    super.key,
    required this.currency,
  });

  final Currency currency;

  @override
  Widget build(BuildContext context) {
    return Consumer<WalletVM>(builder: (context, vm, _) {
      return Scaffold(
        backgroundColor: AppColors.bgWhite,
        body: SafeArea(
          bottom: false,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              top: Sizer.height(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomHeader(
                  showHeader: true,
                  headerText: 'New Beneficiary',
                  onBackBtnTap: () {
                    Navigator.pop(context);
                  },
                ),
                const YBox(30),
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (ctx, i) {
                    final paymentMethod = currency.paymentMethods?[i];
                    return CustomListViews.view(
                      isSvg: true,
                      isNetworkSvg: true,
                      icon: paymentMethod?.icon ?? "",
                      title: paymentMethod?.name ?? "",
                      onTap: () {
                        printty('Single item $paymentMethod');
                        if (paymentMethod?.id == TransferMethod.bankTransfer) {
                          Navigator.pushNamed(
                            context,
                            RoutePath.bankTransferScreen,
                            arguments: BeneficiaryPaymentArg(
                              currency: currency,
                              paymentMethod: paymentMethod!,
                            ),
                          );
                        } else if (paymentMethod?.id ==
                            TransferMethod.mobileMoney) {
                          Navigator.pushNamed(
                            context,
                            RoutePath.mobileMoneyScreen,
                            arguments: BeneficiaryPaymentArg(
                              currency: currency,
                              paymentMethod: paymentMethod!,
                            ),
                          );
                        }
                      },
                    );
                  },
                  separatorBuilder: (_, __) => const YBox(24),
                  itemCount: currency.paymentMethods?.length ?? 0,
                )
              ],
            ),
          ),
        ),
      );
    });
  }
}
