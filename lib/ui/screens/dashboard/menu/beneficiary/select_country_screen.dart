import 'package:flutter_svg/flutter_svg.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SelectCountryScreen extends StatefulWidget {
  const SelectCountryScreen({super.key});

  @override
  State<SelectCountryScreen> createState() => _SelectCountryScreenState();
}

class _SelectCountryScreenState extends State<SelectCountryScreen> {
  @override
  Widget build(BuildContext context) {
    // printty('value ${context.read<CurrencyVM>().currencies.length}');
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ).copyWith(
            top: Sizer.height(20),
          ),
          child: Column(
            children: [
              const CustomHeader(
                showHeader: true,
                headerText: 'Select Country',
              ),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    context.read<CurrencyVM>().getCurrencies();
                  },
                  child: ListView.separated(
                    padding: EdgeInsets.symmetric(
                      vertical: Sizer.height(40),
                    ),
                    shrinkWrap: true,
                    itemBuilder: (ctx, i) {
                      final currency = context.read<CurrencyVM>().currencies[i];
                      return CurrencyRowTile(
                        flag: currency.flag ?? "",
                        country: currency.country ?? "",
                        onTap: () {
                          // printty('country $currency');
                          Navigator.pushNamed(
                            context,
                            (currency.code == CurrencyConstant.cadCurrency ||
                                    currency.code ==
                                        CurrencyConstant.nairaCurrency)
                                ? RoutePath.newBeneficiaryScreen
                                : RoutePath.beneficiaryPreferredPaymentScreen,
                            arguments: currency,
                          );
                        },
                      );
                    },
                    separatorBuilder: (ctx, _) => const YBox(24),
                    itemCount: context.read<CurrencyVM>().currencies.length,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CurrencyRowTile extends StatelessWidget {
  const CurrencyRowTile({
    super.key,
    required this.flag,
    required this.country,
    this.onTap,
  });

  final String flag;
  final String country;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
          vertical: Sizer.height(20),
        ),
        decoration: BoxDecoration(
          color: AppColors.blue100,
          borderRadius: BorderRadius.circular(Sizer.radius(4)),
        ),
        child: Row(
          children: [
            SvgPicture.network(
              flag,
              height: Sizer.height(14),
              width: Sizer.width(20),
            ),
            // imageHelper(
            //   flag,
            //   height: Sizer.height(14),
            //   width: Sizer.width(20),
            // ),
            const XBox(10),
            Text(
              country,
              style: AppTypography.text16.copyWith(
                color: AppColors.textBlack900,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
