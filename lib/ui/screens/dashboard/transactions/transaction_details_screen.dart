// ignore_for_file: use_build_context_synchronously

import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:screenshot/screenshot.dart';

class TransactionDetailsScreen extends StatefulWidget {
  const TransactionDetailsScreen({
    super.key,
    required this.transactionArg,
  });

  final TransactionArg transactionArg;

  @override
  State<TransactionDetailsScreen> createState() =>
      _TransactionDetailsScreenState();
}

class _TransactionDetailsScreenState extends State<TransactionDetailsScreen> {
  ScreenshotController screenshotController = ScreenshotController();

  bool hideExchangeDetails = false;

  @override
  Widget build(BuildContext context) {
    Transaction transaction = widget.transactionArg.transaction;

    return Scaffold(
      backgroundColor: AppColors.primaryBlue,
      body: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Column(
          children: [
            Column(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  color: AppColors.primaryBlue,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      YBox(70),
                      const CustomHeader(
                        showHeader: true,
                        color: AppColors.white,
                        // headerText: 'Transaction Details',
                      ),
                      const YBox(10),
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text:
                                  "${transaction.type?.toLowerCase() == "credit" ? "+" : "-"}${AppUtils.formatAmountDoubleString(transaction.amount ?? "")} ",
                              style: FontTypography.text36.semiBold
                                  .withCustomColor(AppColors.white),
                            ),
                            TextSpan(
                              text: transaction.currency?.code ?? "",
                              style: FontTypography.text20.medium
                                  .withCustomColor(
                                      AppColors.white.withOpacity(0.8)),
                            ),
                          ],
                        ),
                      ),
                      const YBox(4),
                      Text(
                        "${transaction.type?.toLowerCase() == "credit" ? "-" : "+"}${AppUtils.formatAmountDoubleString(transaction.convertedAmount ?? "0")} ${transaction.convertedCurrency?.code ?? ""}",
                        style: FontTypography.text16.medium
                            .withCustomColor(AppColors.blueFE),
                      ),
                      const YBox(65),
                    ],
                  ),
                ),
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Container(
                      width: Sizer.screenWidth,
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(Sizer.radius(20)),
                          topRight: Radius.circular(Sizer.radius(20)),
                        ),
                      ),
                      child: Column(
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: Sizer.width(24),
                            ),
                            child: Column(
                              children: [
                                YBox(55),
                                Center(
                                    child: TransactionDescription(
                                  boldtext: transaction.description ?? '',
                                  rightText:
                                      transaction.category?.toLowerCase() ==
                                              "conversion"
                                          ? " Conversion"
                                          : "",
                                )),
                                const YBox(4),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: Sizer.width(8),
                                    vertical: Sizer.height(4),
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.greenF7,
                                    borderRadius:
                                        BorderRadius.circular(Sizer.radius(10)),
                                  ),
                                  child: Text(
                                    widget.transactionArg.transaction.status ??
                                        "",
                                    style: FontTypography.text12
                                        .withCustomColor(AppColors.green99),
                                  ),
                                ),
                                const YBox(10),
                                SizedBox(
                                  width: Sizer.screenWidth,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      if (transaction.beneficiary != null)
                                        TransactionListTile(
                                          leftText: 'Beneficiary name',
                                          rightText: transaction
                                                  .beneficiary?.accountName ??
                                              '',
                                        ),
                                      if (transaction.beneficiary != null)
                                        TransactionListTile(
                                          leftText: 'Beneficiary’s Bank',
                                          rightText: transaction.beneficiary
                                                  ?.institutionName ??
                                              '',
                                        ),
                                      if (transaction.beneficiary != null)
                                        TransactionListTile(
                                          leftText: 'Beneficiary’s Account #',
                                          rightText: transaction.beneficiary
                                                  ?.accountIdentifier ??
                                              '',
                                        ),
                                      TransactionListTile(
                                        leftText: 'Transaction Type',
                                        rightText: widget.transactionArg
                                                .transaction.category ??
                                            "",
                                        reightTextStyle: FontTypography
                                            .text16.bold
                                            .withCustomColor(
                                          AppColors.primaryBlue,
                                        ),
                                      ),
                                      TransactionListTile(
                                        leftText: 'Fees',
                                        rightText:
                                            "${transaction.fees ?? ""} ${transaction.currency?.code ?? ""}",
                                      ),
                                      if (transaction.rateFormat != null)
                                        AnimatedSize(
                                          duration: Duration(milliseconds: 400),
                                          child: hideExchangeDetails
                                              ? SizedBox.shrink()
                                              : TransactionListTile(
                                                  leftText: 'Exchange Rate',
                                                  rightText:
                                                      transaction.rateFormat ??
                                                          '',
                                                ),
                                        ),
                                    ],
                                  ),
                                ),
                                Column(
                                  children: [
                                    TransactionListTile(
                                      leftText: 'Date/Time',
                                      rightText: AppUtils.formatDateTime((widget
                                                  .transactionArg
                                                  .transaction
                                                  .createdAt ??
                                              DateTime.now())
                                          .toLocal()
                                          .toString()),
                                    ),
                                    // if (transaction.category?.toLowerCase() ==
                                    //     "transfer")
                                    //   TransactionListTile(
                                    //     leftText: 'Recipient',
                                    //     rightText: widget.transactionArg
                                    //             .transaction.destination ??
                                    //         "",
                                    //   ),
                                    if (transaction.source != null)
                                      TransactionListTile(
                                        leftText: 'Source',
                                        rightText: widget.transactionArg
                                                .transaction.source ??
                                            "",
                                      ),
                                    if (transaction.category?.toLowerCase() ==
                                        "transfer")
                                      TransactionListTile(
                                        leftText: 'Sender',
                                        rightText: context
                                                .read<AuthUserVM>()
                                                .user
                                                ?.fullName ??
                                            '',
                                      ),
                                    if (transaction.destination != null &&
                                        transaction.category?.toLowerCase() !=
                                            "transfer")
                                      TransactionListTile(
                                        leftText: 'Destination',
                                        rightText: widget.transactionArg
                                                .transaction.destination ??
                                            "",
                                      ),
                                    if (transaction.sessionId != null)
                                      TransactionListTile(
                                        leftText: 'Session ID',
                                        rightText: widget.transactionArg
                                                .transaction.destination ??
                                            "",
                                        onCopy: () {
                                          Clipboard.setData(ClipboardData(
                                              text: widget.transactionArg
                                                      .transaction.sessionId ??
                                                  ""));

                                          FlushBarToast.fLSnackBar(
                                            message: "Session ID copied",
                                            snackBarType: SnackBarType.success,
                                          );
                                        },
                                      ),
                                    TransactionListTile(
                                      showBorder:
                                          transaction.interacSecurityAnswer !=
                                                  null
                                              ? true
                                              : false,
                                      leftText: 'Reference',
                                      rightText: widget.transactionArg
                                              .transaction.reference ??
                                          "",
                                      onCopy: () {
                                        Clipboard.setData(ClipboardData(
                                            text: widget.transactionArg
                                                    .transaction.reference ??
                                                ""));

                                        FlushBarToast.fLSnackBar(
                                          message:
                                              "Transaction reference copied",
                                          snackBarType: SnackBarType.success,
                                        );
                                      },
                                    ),
                                    if (transaction.interacSecurityAnswer !=
                                        null)
                                      TransactionListTile(
                                        showBorder: false,
                                        leftText: 'Interac Security Answer',
                                        rightText: widget
                                                .transactionArg
                                                .transaction
                                                .interacSecurityAnswer ??
                                            "",
                                      ),
                                  ],
                                ),
                                const YBox(25),
                                // Hide if category is conversion
                                if (transaction.category?.toLowerCase() !=
                                    "conversion")
                                  Row(
                                    children: [
                                      CustomSwitch(
                                        value: hideExchangeDetails,
                                        onChanged: (v) {
                                          hideExchangeDetails = v;
                                          setState(() {});
                                        },
                                      ),
                                      XBox(12),
                                      Text(
                                        "Hide Exchange details",
                                        style: FontTypography.text14
                                            .withCustomColor(
                                          AppColors.gray79,
                                        ),
                                      )
                                    ],
                                  ),
                                const YBox(25),
                                // Row(
                                //   mainAxisAlignment: MainAxisAlignment.center,
                                //   children: [
                                //     CurrencyCard(
                                //       svgPath: AppSvgs.message,
                                //       title: "Report",
                                //       bgColor: AppColors.opacityRed100,
                                //       onTap: () {
                                //         Navigator.pushNamed(
                                //           context,
                                //           RoutePath
                                //               .createFreshDeskTicketWebview,
                                //           arguments: WebViewArg(
                                //             appBarText: "Create Ticket",
                                //             webURL:
                                //                 AppUtils.korrencyCreateTicket,
                                //           ),
                                //         );
                                //       },
                                //     ),
                                //     const XBox(30),
                                //     if (transaction.status?.toLowerCase() ==
                                //         "successful")
                                //       CurrencyCard(
                                //         svgPath: AppSvgs.share,
                                //         title: "Share",
                                //         onTap: () {
                                //           // Navigator.push(
                                //           //     context,
                                //           //     MaterialPageRoute(
                                //           //         builder: (context) => TransactionReceipt(
                                //           //               transactionArg: widget.transactionArg,
                                //           //             )));
                                //         },
                                //       ),
                                //     // if (transaction.status?.toLowerCase() == "successful")
                                //     //   CurrencyCard(
                                //     //     svgPath: AppSvgs.download,
                                //     //     title: "Download",
                                //     //     bgColor: AppColors.litGrey,
                                //     //     onTap: () async {
                                //     //       final hasPermission = await _requestPermissions();
                                //     //       if (hasPermission) {
                                //     //         await _downloadReceiptToDevice();
                                //     //       } else {
                                //     //         _showErrorSnackBar(
                                //     //             "Permission required to save receipt");
                                //     //       }
                                //     //     },
                                //     //   ),
                                //   ],
                                // ),
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.only(
                              top: Sizer.height(24),
                              bottom: Sizer.height(40),
                              left: Sizer.width(24),
                              right: Sizer.width(24),
                            ),
                            decoration: BoxDecoration(
                              border: Border(
                                top: BorderSide(
                                  color: AppColors.grayF4,
                                  width: 1,
                                ),
                              ),
                            ),
                            child: transaction.category?.toLowerCase() ==
                                    "conversion"
                                ? CustomBtn.solid(
                                    borderRadius: BorderRadius.circular(20),
                                    onTap: () {
                                      final isCredit =
                                          transaction.type?.toLowerCase() ==
                                              "credit";
                                      final fromAmount = isCredit
                                          ? transaction.convertedAmount
                                          : transaction.amount;
                                      // final toAmount = isCredit
                                      //     ? transaction.amount
                                      //     : transaction.convertedAmount;
                                      final fromCurrencyCode = isCredit
                                          ? transaction.convertedCurrency?.code
                                          : transaction.currency?.code;
                                      final toCurrencyCode = isCredit
                                          ? transaction.currency?.code
                                          : transaction.convertedCurrency?.code;

                                      Navigator.pushNamed(
                                        context,
                                        RoutePath.convertCurrencyScreen,
                                        arguments: ConvertArg(
                                          fromAmount: fromAmount,
                                          // toAmount: toAmount, // This will be calculated on convert screen cus of rate change
                                          fromCurrencyCode: fromCurrencyCode,
                                          toCurrencyCode: toCurrencyCode,
                                        ),
                                      );
                                    },
                                    text: "Convert Again",
                                  )
                                : Row(
                                    children: [
                                      Expanded(
                                        flex: 2,
                                        child: InkWell(
                                          onTap: () {
                                            Navigator.pushNamed(
                                              context,
                                              RoutePath.sendMoneyScreen,
                                              arguments: SendMoneyArg(
                                                fromAmount: transaction.amount,
                                                fromCurrencyCode: transaction
                                                        .currency?.code ??
                                                    '',
                                                toCurrencyCode: transaction
                                                        .convertedCurrency
                                                        ?.code ??
                                                    '',
                                              ),
                                            );
                                          },
                                          child: Container(
                                            height: Sizer.height(44),
                                            decoration: BoxDecoration(
                                              border: Border.all(
                                                color: AppColors.primaryBlue,
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                            child: Center(
                                              child: Text(
                                                'Send Again',
                                                style: FontTypography
                                                    .text14.medium
                                                    .withCustomColor(
                                                  AppColors.primaryBlue,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      if (transaction.status?.toLowerCase() !=
                                          "pending")
                                        Expanded(
                                          flex: 3,
                                          child: Row(
                                            children: [
                                              Expanded(
                                                child: InkWell(
                                                  onTap: () {
                                                    // BsWrapper.bottomSheet(
                                                    //     context: context,
                                                    //     widget:
                                                    //         RateExperienceModal());
                                                    Navigator.pushNamed(
                                                      context,
                                                      RoutePath
                                                          .transactionReceiptScreen,
                                                      arguments: ReceiptArg(
                                                        transaction:
                                                            transaction,
                                                        isDownload: true,
                                                      ),
                                                    );
                                                  },
                                                  child: Container(
                                                    margin: EdgeInsets.only(
                                                      left: Sizer.width(16),
                                                    ),
                                                    decoration: BoxDecoration(
                                                      color: AppColors.blue9FF,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                    ),
                                                    height: Sizer.height(44),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        svgHelper(
                                                          AppSvgs.download,
                                                          color: AppColors
                                                              .primaryBlue,
                                                          height:
                                                              Sizer.height(24),
                                                          width:
                                                              Sizer.width(24),
                                                        ),
                                                        const XBox(12),
                                                        Text(
                                                          'Download',
                                                          style: FontTypography
                                                              .text14.medium
                                                              .withCustomColor(
                                                            AppColors
                                                                .primaryBlue,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              const XBox(10),
                                              InkWell(
                                                onTap: () async {
                                                  Navigator.pushNamed(
                                                    context,
                                                    RoutePath
                                                        .transactionReceiptScreen,
                                                    arguments: ReceiptArg(
                                                      transaction: transaction,
                                                    ),
                                                  );
                                                },
                                                child: Container(
                                                  padding: EdgeInsets.symmetric(
                                                    horizontal: Sizer.width(12),
                                                    vertical: Sizer.height(10),
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color:
                                                        AppColors.primaryBlue,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                  ),
                                                  child: svgHelper(
                                                    AppSvgs.share,
                                                    color: AppColors.white,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        )
                                    ],
                                  ),
                          )
                        ],
                      ),
                    ),
                    Positioned(
                      top: -40,
                      left: 0,
                      right: 0,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Column(
                            children: [
                              SizedBox(
                                height: Sizer.height(88),
                                width: Sizer.width(88),
                                child: svgHelper(
                                  _getSvg(),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  _getSvg() {
    var category = widget.transactionArg.transaction.category?.toLowerCase();
    var type = widget.transactionArg.transaction.type?.toLowerCase();
    switch (type) {
      case "credit":
        if (category == "p2p" || category == "conversion") {
          return AppSvgs.conversion;
        }
        return AppSvgs.transfer;
      case "debit":
        if (category == "p2p" || category == "conversion") {
          return AppSvgs.conversion;
        }
        return AppSvgs.transfer;
      default:
        return AppSvgs.transfer;
    }
  }

  // _getTextColor(String status) {
  //   switch (status) {
  //     case "successful":
  //       return AppColors.iconGreen;
  //     case "pending":
  //       return AppColors.pending;
  //     default:
  //       return AppColors.failed;
  //   }
  // }

  // Future<bool> _requestPermissions() async {
  //   if (Platform.isAndroid) {
  //     final androidInfo = await DeviceInfoPlugin().androidInfo;
  //     final sdkInt = androidInfo.version.sdkInt;

  //     if (sdkInt >= 33) {
  //       // Android 13+
  //       final status = await Permission.photos.request();
  //       return status.isGranted;
  //     } else if (sdkInt >= 30) {
  //       // Android 11-12
  //       final status = await Permission.storage.request();
  //       return status.isGranted;
  //     } else {
  //       // Android 10 and below
  //       final status = await Permission.storage.request();
  //       return status.isGranted;
  //     }
  //   } else if (Platform.isIOS) {
  //     final status = await Permission.photos.request();
  //     return status.isGranted;
  //   }

  //   return false;
  // }
}

class TransactionDescription extends StatelessWidget {
  const TransactionDescription({
    super.key,
    this.leftText,
    required this.boldtext,
    this.rightText,
  });
  final String? leftText;
  final String boldtext;
  final String? rightText;

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        children: [
          if (leftText != null)
            TextSpan(
              text: leftText,
              style: FontTypography.text16.withCustomColor(AppColors.gray79),
            ),
          TextSpan(
            text: boldtext,
            style: FontTypography.text16.bold.withCustomColor(AppColors.gray79),
          ),
          if (rightText != null)
            TextSpan(
              text: rightText,
              style: FontTypography.text16.withCustomColor(AppColors.gray79),
            ),
        ],
      ),
    );
  }
}
