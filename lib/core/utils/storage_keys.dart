class StorageKey {
  static const String accessToken = "accessToken";
  static const String refreshToken = "refreshToken";
  static const String authUser = "authUser";
  static const String email = "email";

  static const String deviceToken = "deviceToken";
  static const String deviceId = "deviceId";
  static const String deviceType = "deviceType";
  static const String deviceName = "deviceName";
  static const String deviceModel = "deviceModel";

  static const String logoutCount = "logoutCount";

  static const String notificationToken = "notificationToken";
  static const String appVersion = "appVersion";
  static const String hasSeenOfferHowitWorksScreen =
      "hasSeenOfferHowitWorksScreen";
  static const String hasSeenP2pWelcomeScreen = "hasSeenP2pWelcomeScreen";

  static const String fingerPrintIsEnabled = "fingerPrintIsEnabled";
  static const String userHasSeenBiometricLoginPrompt = "biometricLoginPrompt";
  static const String ipAddress = "ipAddress";

  static const String deepLinkValue = "deep_link_value";
  static const String latitude = "latitude";
  static const String longitude = "longitude";
  static const String userAttribution = "user_attribution";
}
