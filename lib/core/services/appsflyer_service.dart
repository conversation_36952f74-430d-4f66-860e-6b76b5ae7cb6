import 'dart:async';
import 'dart:io';

import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:korrency/core/core.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppsFlyerService {
  static AppsFlyerService? _instance;
  static AppsFlyerService get instance => _instance ??= AppsFlyerService._();

  AppsFlyerService._();

  late AppsflyerSdk _appsflyerSdk;
  bool _isInitialized = false;

  final String _appsFlyerDevKey = dotenv.env["AF_DEV_KEY"] ?? '';
  final String _appleAppId = dotenv.env["AF_APP_ID"] ?? ''; // iOS only

  // For Android, we use the package name instead of the iOS App Store ID
  String get _appId {
    if (Platform.isIOS) {
      return _appleAppId;
    } else {
      // For Android, return empty string - AppsFlyer will use package name automatically
      return '';
    }
  }

  // Callbacks
  StreamController<Map<String, dynamic>>? _conversionDataController;
  StreamController<Map<String, dynamic>>? _deepLinkController;

  Stream<Map<String, dynamic>>? get conversionDataStream =>
      _conversionDataController?.stream;
  Stream<Map<String, dynamic>>? get deepLinkStream =>
      _deepLinkController?.stream;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Get package info for debugging
      final packageInfo = await PackageInfo.fromPlatform();

      // Debug configuration
      printty('🔧 AppsFlyer Configuration:');
      printty('  Platform: ${Platform.isIOS ? 'iOS' : 'Android'}');
      printty('  Package Name: ${packageInfo.packageName}');
      printty(
          '  Dev Key: ${_appsFlyerDevKey.isNotEmpty ? 'Found' : 'Missing'}');
      printty(
          '  App ID: ${_appId.isNotEmpty ? _appId : 'Using package name (Android)'}');

      // Important: Check if package name is registered in AppsFlyer
      if (Platform.isAndroid) {
        printty(
            '⚠️  IMPORTANT: Ensure "${packageInfo.packageName}" is registered in your AppsFlyer dashboard');
        printty(
            '   If you get 403 errors, this package name might not be configured in AppsFlyer');
      }
      // Initialize stream controllers
      _conversionDataController =
          StreamController<Map<String, dynamic>>.broadcast();
      _deepLinkController = StreamController<Map<String, dynamic>>.broadcast();

      // Configure AppsFlyer options
      AppsFlyerOptions appsFlyerOptions = AppsFlyerOptions(
        afDevKey: _appsFlyerDevKey,
        appId: _appId, // Use platform-specific app ID
        showDebug: true, // Set to false in production
        timeToWaitForATTUserAuthorization: 60,
        disableAdvertisingIdentifier: false,
        disableCollectASA: false,
      );

      // Initialize SDK
      _appsflyerSdk = AppsflyerSdk(appsFlyerOptions);

      // Set up callbacks
      _setupCallbacks();

      // Initialize the SDK
      await _appsflyerSdk.initSdk(
        registerConversionDataCallback: true,
        registerOnAppOpenAttributionCallback: true,
        registerOnDeepLinkingCallback: true,
      );

      _isInitialized = true;
      printty('✅ AppsFlyer SDK initialized successfully');
    } catch (e) {
      printty('❌ AppsFlyer initialization error: $e');
      rethrow;
    }
  }

  void _setupCallbacks() {
    // Conversion data callback
    _appsflyerSdk.onInstallConversionData((data) {
      printty('📊 Conversion Data: $data');
      _conversionDataController?.add(data);

      // Process attribution data
      _processAttributionData(data);
    });

    // App open attribution callback
    _appsflyerSdk.onAppOpenAttribution((data) {
      printty('📱 App Open Attribution: $data');
      _conversionDataController?.add(data);

      // Process attribution data for app open events
      _processAttributionData(data);
    });

    // Deep link callback
    _appsflyerSdk.onDeepLinking((data) {
      printty('🔗 Deep Link Data: $data');
      printty('🔗 Deep Link Status: ${data.status}');

      switch (data.status) {
        case Status.FOUND:
          printty('✅ Deep link FOUND');
          printty('🔗 Click Event: ${data.deepLink?.clickEvent}');
          printty('🔗 Deep Link Value: ${data.deepLink?.deepLinkValue}');
          printty('🔗 Match Type: ${data.deepLink?.matchType}');

          if (data.deepLink?.clickEvent != null) {
            _deepLinkController?.add(data.deepLink!.clickEvent);
          }
          break;

        case Status.NOT_FOUND:
          printty('❌ Deep link NOT_FOUND - No deep link detected');
          break;

        case Status.ERROR:
          printty('❌ Deep link ERROR: ${data.error}');
          break;

        case Status.PARSE_ERROR:
          printty('❌ Deep link PARSE_ERROR - Could not parse the deep link');
          break;
      }
    });
  }

  /// Process attribution data and send to Attribution Service
  void _processAttributionData(Map<String, dynamic> data) {
    try {
      // Send attribution data to Attribution Service for processing
      AttributionService.instance.processAppsFlyerData(data);
    } catch (e) {
      printty('❌ Error processing attribution data: $e');
    }
  }

  // Start tracking
  Future<void> startTracking() async {
    if (!_isInitialized) {
      throw Exception('AppsFlyer SDK not initialized');
    }

    try {
      _appsflyerSdk.startSDK();
      printty('▶️ AppsFlyer tracking started');
    } catch (e) {
      printty('❌ Error starting AppsFlyer tracking: $e');
      rethrow;
    }
  }

  // Stop tracking
  Future<void> stopTracking() async {
    try {
      _appsflyerSdk.stop(true);
      printty('⏹️ AppsFlyer tracking stopped');
    } catch (e) {
      printty('❌ Error stopping AppsFlyer tracking: $e');
    }
  }

  // Get AppsFlyer ID
  Future<String?> getAppsFlyerId() async {
    try {
      return await _appsflyerSdk.getAppsFlyerUID();
    } catch (e) {
      printty('❌ Error getting AppsFlyer ID: $e');
      return null;
    }
  }

  // Basic event tracking
  Future<void> logEvent(
      String eventName, Map<String, dynamic>? eventValues) async {
    if (!_isInitialized) {
      printty('⚠️ AppsFlyer SDK not initialized');
      return;
    }

    try {
      printty('📤 Attempting to log event: $eventName');
      printty('📤 Event values: $eventValues');
      printty('📤 Dev Key configured: ${_appsFlyerDevKey.isNotEmpty}');

      final res = await _appsflyerSdk.logEvent(eventName, eventValues);
      printty('✅ Event logged successfully: $eventName');
      printty('📝 Response: $res');
    } catch (e) {
      printty('❌ Error logging event "$eventName": $e');
      printty(
          '🔍 Check if AppsFlyer dev key is valid and app is properly configured');

      // Additional debugging for 403 errors
      if (e.toString().contains('403') || e.toString().contains('Forbidden')) {
        printty('🚨 403 Forbidden Error - Possible causes:');
        printty('  1. Invalid AppsFlyer Dev Key');
        printty('  2. App not registered in AppsFlyer dashboard');
        printty('  3. Package name mismatch');
        printty('  4. AppsFlyer account suspended or restricted');
      }
    }
  }

  // Set user ID
  Future<void> setUserId(String userId) async {
    try {
      _appsflyerSdk.setCustomerUserId(userId);
      printty('👤 User ID set: $userId');
    } catch (e) {
      printty('❌ Error setting user ID: $e');
    }
  }

  // Set additional user properties
  Future<void> setUserProperties(Map<String, dynamic> properties) async {
    try {
      _appsflyerSdk.setAdditionalData(properties);
      printty('👥 User properties set: $properties');
    } catch (e) {
      printty('❌ Error setting user properties: $e');
    }
  }

  // Revenue tracking
  Future<void> logRevenue(
    double revenue,
    String currency, {
    String? orderId,
    String? productId,
    Map<String, dynamic>? additionalParameters,
  }) async {
    Map<String, dynamic> eventValues = {
      'af_revenue': revenue,
      'af_currency': currency,
    };

    if (orderId != null) eventValues['af_order_id'] = orderId;
    if (productId != null) eventValues['af_content_id'] = productId;
    if (additionalParameters != null) eventValues.addAll(additionalParameters);

    await logEvent('af_purchase', eventValues);
  }

  // Enhanced event tracking methods for key funnel events

  /// Track Create Account event
  Future<void> logCreateAccount({
    required String userId,
    String? method,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final eventValues = <String, dynamic>{
      'af_customer_user_id': userId,
      if (method != null) 'registration_method': method,
      'event_category': 'user_acquisition',
      ...?additionalParameters,
    };

    await logEvent('create_account', eventValues);
  }

  /// Track Complete KYC event
  Future<void> logCompleteKyc({
    required String userId,
    String? kycLevel,
    String? verificationMethod,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final eventValues = <String, dynamic>{
      'af_customer_user_id': userId,
      if (kycLevel != null) 'kyc_level': kycLevel,
      if (verificationMethod != null) 'verification_method': verificationMethod,
      'event_category': 'user_verification',
      ...?additionalParameters,
    };

    await logEvent('complete_kyc', eventValues);
  }

  /// Track Deposit Money event
  Future<void> logDepositMoney({
    required String userId,
    required double amount,
    required String currency,
    String? paymentMethod,
    String? corridor,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final eventValues = <String, dynamic>{
      'af_customer_user_id': userId,
      'af_revenue': amount,
      'af_currency': currency,
      if (paymentMethod != null) 'payment_method': paymentMethod,
      if (corridor != null) 'corridor': corridor,
      'event_category': 'financial_action',
      'event_type': 'deposit',
      ...?additionalParameters,
    };

    await logEvent('deposit_money', eventValues);
  }

  /// Track Send Money event
  Future<void> logSendMoney({
    required String userId,
    required double amount,
    required String fromCurrency,
    required String toCurrency,
    required String corridor,
    String? transactionId,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final eventValues = <String, dynamic>{
      'af_customer_user_id': userId,
      'af_revenue': amount,
      'af_currency': fromCurrency,
      'from_currency': fromCurrency,
      'to_currency': toCurrency,
      'corridor': corridor,
      if (transactionId != null) 'transaction_id': transactionId,
      'event_category': 'financial_action',
      'event_type': 'money_transfer',
      ...?additionalParameters,
    };

    await logEvent('send_money', eventValues);
  }

  /// Check if AppsFlyer is initialized
  bool get isInitialized => _isInitialized;

  void dispose() {
    _conversionDataController?.close();
    _deepLinkController?.close();
  }
}

// class AppsflyService {
//   static late AppsflyerSdk appsflyerSdk;

//   static init() async {
//     printty("Initializing AppsFlyer SDK...");

//     // Get the AppsFlyer dev key and app ID from environment variables
//     final afDevKey = dotenv.env["AF_DEV_KEY"];
//     final appId = dotenv.env["AF_APP_ID"];

//     printty("AF_DEV_KEY: ${afDevKey != null ? 'Found' : 'Missing'}");
//     printty("AF_APP_ID: ${appId != null ? 'Found' : 'Missing'}");

//     AppsFlyerOptions appsFlyerOptions = AppsFlyerOptions(
//       afDevKey: afDevKey!,
//       appId: appId!,
//       showDebug: true,
//       timeToWaitForATTUserAuthorization: 15, // for iOS 14.5
//       appInviteOneLink: "9BIc", // OneLink ID from your referral link
//       disableAdvertisingIdentifier: false,
//       disableCollectASA: false,
//     );

//     appsflyerSdk = AppsflyerSdk(appsFlyerOptions);

//     appsflyerSdk.onInstallConversionData((res) {
//       printty("onInstallConversionData: $res");
//     });

//     appsflyerSdk.onAppOpenAttribution((res) {
//       printty("onAppOpenAttribution: $res");
//     });

//     appsflyerSdk.onDeepLinking((DeepLinkResult dp) {
//       printty("onDeepLinking: $dp");

//       printty(
//           "AppsFlyer deep linking callback triggered with status: ${dp.status}");

//       switch (dp.status) {
//         case Status.FOUND:
//           printty("Deep link FOUND - Full DeepLinkResult: $dp");
//           printty("Deep link clickEvent: ${dp.deepLink?.clickEvent}");

//           if (dp.deepLink?.clickEvent != null &&
//               dp.deepLink?.clickEvent["link"] != null) {
//             final link = dp.deepLink?.clickEvent["link"];
//             printty("Deep link URL: $link");

//             try {
//               if (NavigatorKeys.appNavigatorKey.currentContext != null) {
//                 printty("Context available, extracting deep link value");
//                 NavigatorKeys.appNavigatorKey.currentContext!
//                     .read<OnBoardVM>()
//                     .extractDeepLinkValue(link);
//                 printty("Deep link value extraction completed");
//               } else {
//                 printty(
//                     "ERROR: Navigator context is null, cannot process deep link");
//               }
//             } catch (e) {
//               printty("ERROR processing deep link: ${e.toString()}");
//               printty("Stack trace: ${StackTrace.current}");
//             }
//           } else {
//             printty("Deep link found but missing required data:");
//             if (dp.deepLink?.clickEvent == null) {
//               printty("- clickEvent is null");
//             }
//             if (dp.deepLink?.clickEvent != null &&
//                 dp.deepLink?.clickEvent["link"] == null) {
//               printty("- link is null");
//             }
//           }
//           break;

//         case Status.NOT_FOUND:
//           printty("Deep link NOT_FOUND - No deep link detected");
//           break;

//         case Status.ERROR:
//           printty("Deep link ERROR: ${dp.error}");
//           break;

//         case Status.PARSE_ERROR:
//           printty("Deep link PARSE_ERROR - Could not parse the deep link");
//           break;
//       }
//     });

//     appsflyerSdk.initSdk(
//         registerConversionDataCallback: true,
//         registerOnAppOpenAttributionCallback: true,
//         registerOnDeepLinkingCallback: true);

//     await logEvent("appInit", {"App": "App Open"});

//     // printty(s.toString());
//     printty("===> appflyer initiated...");
//   }

//   static Future<bool?> logEvent(String eventName, Map? eventValues) async {
//     bool? result = false;
//     printty(appsflyerSdk.toString());
//     printty("-==-=-=-=121");
//     try {
//       result = await appsflyerSdk.logEvent(eventName, eventValues);
//       printty(result.toString());
//       printty("app flyer ok=====>");
//       return result;
//     } on Exception catch (_) {
//       return result;
//     }
//   }
// }
