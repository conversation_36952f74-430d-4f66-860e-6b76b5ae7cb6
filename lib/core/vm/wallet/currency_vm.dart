import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:korrency/core/core.dart';

class CurrencyVM extends BaseVM {
  // final _currencyProvider = locator<CurrencyProvider>();

  List<Currency> _currencies = [];
  List<Currency> get currencies => _currencies;
  List<Currency> _africanCurrencies = [];
  List<Currency> get africanCurrencies => _africanCurrencies;
  Currency? _selectedCureency;
  Currency? get selectedCureency => _selectedCureency;

  List<Currency> get creatableCurrencies =>
      _currencies.where((element) => element.isCreatable == true).toList();

  Currency? get nairaCurrency => _currencies.firstWhereOrNull(
        (element) => element.code == "NGN",
      );

  Currency? get cadCurrency => _currencies.firstWhereOrNull(
        (element) => element.code == "CAD",
      );

  List<CurrencyRates> allCurrenciesRates = [];
  List<CurrencyRates> _isCreatableCurrenciesRates = [];
  List<CurrencyRates> get isCreatableCurrenciesRates =>
      _isCreatableCurrenciesRates;

  CurrencyRates? _selectedCurrencyRate;
  CurrencyRates? get selectedCurrencyRate => _selectedCurrencyRate;

  CurrencyLimits? _selectedCurrencyLimit;
  CurrencyLimits? get selectedCurrencyLimit => _selectedCurrencyLimit;

  List<CurrencyLimits> allCurrenciesLimits = [];
  List<CurrencyLimits> _isCreatableCurrenciesLimits = [];
  List<CurrencyLimits> get isCreatableCurrenciesLimits =>
      _isCreatableCurrenciesLimits;

  setSelectedCurrency(Currency currency) {
    _selectedCureency = currency;
    reBuildUI();
  }

  setSelectedCurrencyRate(String code) {
    _selectedCurrencyRate = _isCreatableCurrenciesRates.firstWhereOrNull(
      (element) => element.code == code,
    );
    reBuildUI();
  }

  setSelectedCurrencyLimit(String? code) {
    _selectedCurrencyLimit = _isCreatableCurrenciesLimits.firstWhereOrNull(
      (element) => element.code == code,
    );
    reBuildUI();
  }

  Future<ApiResponse> getCurrencies() async {
    return await performApiCall(
      url: "/currencies",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var currencies = currencyFromJson(json.encode(data["data"]));
        _currencies = currencies;
        return ApiResponse(success: true, data: currencies);
      },
    );
  }

  // Get African Currencies for Frequent Recipient Country
  Future<ApiResponse> getAfricanCurrencies() async {
    return await performApiCall(
      url: "/currencies/africa",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var currencies = currencyFromJson(json.encode(data["data"]));
        _africanCurrencies = currencies;
        return ApiResponse(success: true, data: currencies);
      },
    );
  }

  Future<ApiResponse> updateFreqAfricanCountry(int currencyId) async {
    return await performApiCall(
      url: "/auth/profile/update",
      method: apiService.postWithAuth,
      body: {"frequent_destination_currency_id": currencyId},
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getCurrenciesRates() async {
    return await performApiCall(
      url: "/currencies/rates",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var currenciesRates = currencyRatesFromJson(json.encode(data["data"]));
        allCurrenciesRates = currenciesRates;
        _isCreatableCurrenciesRates = currenciesRates
            .where((element) => element.isCreatable == true)
            .toList();
        return ApiResponse(success: true, data: currenciesRates);
      },
    );
  }

  Future<ApiResponse> getCurrenciesLimits() async {
    return await performApiCall(
      url: "/currencies/limits",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var currenciesLimits =
            currencyLimitsFromJson(json.encode(data["data"]));
        // printty(currenciesLimits.first.code, level: 'first code');
        allCurrenciesLimits = currenciesLimits;
        _isCreatableCurrenciesLimits = currenciesLimits
            .where((element) => element.isCreatable == true)
            .toList();
        setSelectedCurrencyLimit(currenciesLimits.first.code);
        return ApiResponse(success: true, data: currenciesLimits);
      },
    );
  }
}
